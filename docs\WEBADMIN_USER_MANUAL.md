# WebAdmin 管理后台操作说明书

## 文档信息

- **项目名称**: SlzrCrossGate WebAdmin 终端管理系统
- **文档版本**: v1.0
- **创建日期**: 2025-08-27
- **文档类型**: 用户操作手册
- **适用范围**: 系统管理员、商户管理员、操作员

## 目录

1. [系统登录](#1-系统登录)
2. [主界面导航](#2-主界面导航)
3. [概览监控](#3-概览监控)
4. [终端管理](#4-终端管理)
5. [内容管理](#5-内容管理)
6. [业务配置](#6-业务配置)
7. [系统管理](#7-系统管理)
8. [审计日志](#8-审计日志)
9. [个人设置](#9-个人设置)
10. [常见问题](#10-常见问题)

---

## 1. 系统登录

### 1.1 登录页面

WebAdmin系统支持多种登录方式，包括用户名密码登录、双因素认证。

![图 3](WEBADMIN_USER_MANUAL/f7d8352712f89eb678ca9549445b66f42a01fc779dd27e3c519001e691d0c5be.png)  


#### 1.1.1 用户名密码登录

**操作步骤**：
1. 在浏览器中访问WebAdmin系统地址（如：http://your-server:18822）
2. 在登录页面输入用户名和密码
3. 点击"登录"按钮
4. 如果启用了双因素认证，系统会跳转到验证码输入页面

**注意事项**：
- 用户名不区分大小写
- 密码区分大小写
- 连续登录失败可能会触发账户锁定

#### 1.1.2 双因素认证(2FA)

如果用户启用了双因素认证，登录时需要额外的验证步骤。

[配合-LOGIN02] 请提供双因素认证页面的截图，包含：
- 验证码输入框
- 提示信息
- 确认按钮
- 返回登录按钮

**操作步骤**：
1. 完成用户名密码验证后，系统跳转到2FA验证页面
2. 打开手机上的身份验证器应用（如Google Authenticator）
3. 输入6位动态验证码
4. 点击"验证"按钮完成登录

#### 1.1.3 微信扫码登录

[配合-LOGIN03] 请提供微信扫码登录页面的截图，包含：
- 微信二维码
- 扫码提示信息
- 返回普通登录的选项

**操作步骤**：
1. 在登录页面点击"微信登录"
2. 使用微信扫描页面上的二维码
3. 在微信中确认登录
4. 系统自动完成登录并跳转到主界面

**注意事项**：
- 需要先绑定微信账号才能使用微信登录
- 二维码有效期为5分钟

### 1.2 首次登录设置

#### 1.2.1 强制密码更改

如果系统要求强制更改密码，登录后会跳转到密码更改页面。

[配合-LOGIN04] 请提供强制密码更改页面的截图

**操作步骤**：
1. 输入当前密码
2. 输入新密码（需符合密码策略要求）
3. 确认新密码
4. 点击"更改密码"按钮

#### 1.2.2 双因素认证设置

首次登录或管理员要求时，需要设置双因素认证。

[配合-LOGIN05] 请提供2FA设置页面的截图，包含：
- 二维码显示
- 密钥文本
- 验证码输入框
- 设置步骤说明

**操作步骤**：
1. 在手机上安装身份验证器应用
2. 扫描页面上的二维码或手动输入密钥
3. 输入身份验证器生成的6位验证码
4. 点击"启用双因素认证"按钮

---

## 2. 主界面导航

### 2.1 整体布局

WebAdmin采用现代化的响应式布局，包含顶部导航栏、左侧菜单栏和主内容区域。

[配合-NAV01] 请提供主界面整体布局的截图，包含：
- 顶部导航栏
- 左侧菜单栏
- 主内容区域
- 用户信息显示

#### 2.1.1 顶部导航栏

顶部导航栏包含以下功能：
- **菜单折叠按钮**：控制左侧菜单的展开/折叠
- **系统标题**：显示"WebAdmin"
- **用户信息**：显示当前登录用户信息
- **主题切换**：切换明暗主题
- **用户菜单**：包含个人设置、退出登录等选项

[配合-NAV02] 请提供顶部导航栏的详细截图，特别是用户菜单下拉选项

#### 2.1.2 左侧菜单栏

左侧菜单栏采用分组结构，根据用户角色动态显示可访问的功能模块：

**菜单分组结构**：
1. **概览监控**
   - 仪表盘

2. **终端管理**
   - 终端管理
   - 终端记录
   - 终端事件
   - 终端日志
   - 终端文件上传

3. **内容管理**
   - 文件类型
   - 文件版本
   - 文件发布
   - 预约发布

4. **业务配置**
   - 线路参数
   - 银联密钥
   - 车辆管理
   - 字典管理
   - 未注册线路

5. **系统管理**
   - 用户管理
   - 角色管理
   - 商户管理
   - 菜单管理
   - 系统设置
   - 功能权限

6. **审计日志**
   - 登录日志
   - 操作日志
   - 密码变更日志

7. **客户功能**（可选）
   - 客户演示功能

[配合-NAV03] 请提供左侧菜单栏的完整截图，显示所有菜单分组和菜单项

#### 2.1.3 响应式设计

系统支持桌面端和移动端访问：
- **桌面端**：左侧菜单可折叠，支持图标模式
- **移动端**：左侧菜单变为抽屉式，点击遮罩层关闭

[配合-NAV04] 请提供移动端界面的截图

### 2.2 权限控制

不同角色用户看到的菜单项会有所不同：

**角色权限说明**：
- **SystemAdmin（系统管理员）**：可访问所有功能
- **MerchantAdmin（商户管理员）**：可访问商户相关功能，不能访问系统级设置
- **Operator（操作员）**：只能访问基本的查看和操作功能

**权限控制特点**：
- 菜单项根据用户角色动态显示/隐藏
- 页面内的操作按钮也会根据功能权限控制
- 无权限访问的功能会显示相应提示

---

## 3. 概览监控

### 3.1 仪表盘

仪表盘是系统的首页，提供系统整体运行状况的概览信息。

[配合-DASH01] 请提供仪表盘页面的完整截图，包含：
- 统计卡片
- 图表展示
- 实时数据
- 快捷操作入口

#### 3.1.1 统计概览

仪表盘顶部显示关键业务指标的统计卡片：
- **终端总数**：系统中注册的终端设备总数
- **在线终端**：当前在线的终端设备数量
- **离线终端**：当前离线的终端设备数量
- **今日交易**：当天的交易记录数量

[配合-DASH02] 请提供统计卡片区域的截图

#### 3.1.2 图表分析

仪表盘中部显示各种分析图表：
- **终端状态分布图**：饼图显示在线/离线终端比例
- **交易趋势图**：折线图显示最近7天的交易趋势
- **商户活跃度**：柱状图显示各商户的终端活跃情况

[配合-DASH03] 请提供图表区域的截图

#### 3.1.3 实时监控

仪表盘底部显示实时监控信息：
- **最近上线终端**：显示最近上线的终端设备列表
- **系统告警**：显示系统异常和告警信息
- **待处理任务**：显示需要处理的任务数量

[配合-DASH04] 请提供实时监控区域的截图

---

## 4. 终端管理

终端管理是WebAdmin的核心功能模块，提供终端设备的全生命周期管理。

### 4.1 终端管理主页

终端管理主页显示所有终端设备的列表，支持多种筛选和操作功能。

[配合-TERM01] 请提供终端管理主页的完整截图，包含：
- 搜索和筛选区域
- 终端列表表格
- 操作按钮
- 分页控件

#### 4.1.1 搜索和筛选

页面顶部提供多种搜索和筛选选项：
- **关键词搜索**：支持按终端ID、设备编号、车牌号等搜索
- **商户筛选**：下拉选择特定商户（商户管理员只能看到自己的商户）
- **线路筛选**：按线路编号或线路名称筛选
- **状态筛选**：按在线/离线状态筛选
- **时间范围**：按注册时间或最后活跃时间筛选

[配合-TERM02] 请提供搜索筛选区域的截图

#### 4.1.2 终端列表

终端列表采用响应式表格设计，支持动态字段配置：

**默认显示字段**：
- **商户**：终端所属商户名称（鼠标悬停显示商户ID）
- **出厂序列号**：设备的出厂序列号
- **设备编号**：终端的设备编号
- **车牌号**：关联的车辆车牌号
- **线路编号**：终端所在线路的编号
- **线路名称**：终端所在线路的名称
- **终端类型**：设备类型标识
- **注册日期**：终端首次注册的时间
- **最后活跃时间**：终端最后一次通信的时间
- **状态**：在线/离线状态标识

**文件版本信息**：
- **APP版本**：终端当前运行的应用程序版本
- **票价版本**：终端当前的票价文件版本
- **黑名单版本**：终端当前的黑名单文件版本
- **交通部白名单**：交通部白名单文件版本

**设备属性信息**：
- **未传数**：未上传的交易记录数量
- **票价**：当前设置的票价
- **司机卡号**：当前登录的司机卡号
- **流量卡SIM号**：设备使用的SIM卡号码
- **交通部PSAM卡号**：交通部PSAM卡号码
- **岭南通PSAM卡号**：岭南通PSAM卡号码
- **序列号**：设备序列号

[配合-TERM03] 请提供终端列表表格的截图，显示各种字段信息

#### 4.1.3 字段配置

系统支持动态配置终端列表显示的字段：
- 点击表格右上角的"列设置"按钮
- 在弹出的对话框中选择要显示的字段
- 支持拖拽调整字段顺序
- 配置会保存到本地存储

[配合-TERM04] 请提供字段配置对话框的截图

#### 4.1.4 操作功能

每个终端记录提供以下操作：
- **查看详情**：查看终端的详细信息
- **发送消息**：向终端发送消息（需要相应权限）
- **发布文件**：向终端发布文件（需要相应权限）
- **查看事件**：查看终端的事件记录
- **查看日志**：查看终端的日志记录

[配合-TERM05] 请提供终端操作按钮的截图

### 4.2 终端详情页面

点击终端记录的"查看详情"按钮，进入终端详情页面。

[配合-TERM06] 请提供终端详情页面的完整截图

#### 4.2.1 基本信息

显示终端的基本信息：
- 终端ID、设备编号、出厂序列号
- 所属商户、线路信息、车辆信息
- 注册时间、最后活跃时间
- 当前状态和连接信息

#### 4.2.2 实时状态

显示终端的实时运行状态：
- 连接状态、登录时间、IP地址
- 设备属性信息（票价、司机卡号等）
- 文件版本信息
- 设备性能指标

#### 4.2.3 操作历史

显示终端的最近操作历史：
- 消息发送记录
- 文件发布记录
- 状态变更记录
- 异常事件记录

### 4.3 终端记录管理

终端记录页面显示终端的交易和业务记录。

[配合-TERM07] 请提供终端记录页面的截图

**功能特点**：
- 支持按商户、终端、时间范围筛选
- 显示交易类型、金额、时间等信息
- 支持记录导出功能
- 提供数据统计分析

### 4.4 终端事件管理

终端事件页面显示终端的各种事件记录。

[配合-TERM08] 请提供终端事件页面的截图

**事件类型**：
- 设备上线/下线事件
- 文件更新事件
- 异常报警事件
- 业务操作事件

**功能特点**：
- 实时事件监控
- 事件级别分类（信息、警告、错误）
- 支持事件搜索和筛选
- 事件详情查看

### 4.5 终端日志管理

终端日志页面显示终端的详细日志信息。

[配合-TERM09] 请提供终端日志页面的截图

**日志类型**：
- 系统运行日志
- 业务操作日志
- 错误异常日志
- 通信协议日志

**功能特点**：
- 支持日志级别筛选
- 时间范围查询
- 关键词搜索
- 日志导出功能

### 4.6 终端文件上传管理

终端文件上传页面管理终端上传的各种文件。

[配合-TERM10] 请提供终端文件上传页面的截图

**功能特点**：
- 显示上传文件列表
- 支持文件下载
- 文件完整性验证
- 上传进度监控

---

## 5. 内容管理

内容管理模块负责管理终端设备所需的各种文件，包括应用程序、配置文件、数据文件等。

### 5.1 文件类型管理

文件类型管理页面用于定义和管理系统中的文件类型。

[配合-FILE01] 请提供文件类型管理页面的截图，包含：
- 文件类型列表
- 添加/编辑文件类型的表单
- 操作按钮

#### 5.1.1 文件类型列表

显示系统中已定义的文件类型：
- **文件类型ID**：唯一标识符
- **文件类型名称**：显示名称
- **描述**：文件类型的详细说明
- **所属商户**：文件类型所属的商户
- **创建时间**：创建日期

#### 5.1.2 添加文件类型

**操作步骤**：
1. 点击"添加文件类型"按钮
2. 填写文件类型信息：
   - 文件类型ID（必填，不可重复）
   - 文件类型名称（必填）
   - 描述信息（可选）
3. 点击"保存"按钮

[配合-FILE02] 请提供添加文件类型对话框的截图

#### 5.1.3 编辑文件类型

**操作步骤**：
1. 在文件类型列表中点击"编辑"按钮
2. 修改文件类型信息（ID不可修改）
3. 点击"保存"按钮

**注意事项**：
- 已被文件版本使用的文件类型不能删除
- 文件类型ID创建后不能修改

### 5.2 文件版本管理

文件版本管理页面用于上传和管理各种文件的版本。

[配合-FILE03] 请提供文件版本管理页面的截图，包含：
- 文件版本列表
- 上传文件功能
- 筛选和搜索功能

#### 5.2.1 文件版本列表

显示系统中的所有文件版本：
- **文件类型**：文件所属的类型
- **文件参数**：文件的参数信息
- **版本号**：文件的版本标识
- **文件大小**：文件的大小
- **CRC校验值**：文件完整性校验值
- **上传时间**：文件上传的时间
- **操作员**：上传文件的操作员
- **备注**：文件的备注信息

#### 5.2.2 上传文件版本

**操作步骤**：
1. 点击"上传文件"按钮
2. 在上传对话框中填写信息：
   - 选择文件类型
   - 输入文件参数（可选）
   - 输入版本号
   - 选择要上传的文件
   - 输入备注信息（可选）
3. 点击"上传"按钮

[配合-FILE04] 请提供文件上传对话框的截图

**注意事项**：
- 支持的文件格式根据文件类型而定
- 文件大小限制为100MB
- 版本号在同一文件类型下必须唯一
- 系统会自动计算文件的CRC校验值

#### 5.2.3 文件版本操作

每个文件版本提供以下操作：
- **下载**：下载文件到本地
- **发布**：将文件发布到指定终端
- **查看详情**：查看文件的详细信息
- **删除**：删除文件版本（需要相应权限）

[配合-FILE05] 请提供文件版本操作按钮的截图

### 5.3 文件发布管理

文件发布管理页面用于管理文件的发布任务。

[配合-FILE06] 请提供文件发布管理页面的截图

#### 5.3.1 发布文件

**操作步骤**：
1. 在文件版本列表中点击"发布"按钮
2. 在发布对话框中设置：
   - 发布类型：选择按线路发布或按终端发布
   - 发布目标：选择具体的线路或终端
   - 发布备注：输入发布说明（可选）
3. 点击"确认发布"按钮

[配合-FILE07] 请提供文件发布对话框的截图

#### 5.3.2 发布记录

显示所有的文件发布记录：
- **文件信息**：文件类型、版本号
- **发布类型**：线路发布或终端发布
- **发布目标**：具体的线路或终端
- **发布时间**：发布任务创建时间
- **发布状态**：发布任务的执行状态
- **操作员**：执行发布的操作员

### 5.4 预约发布管理

预约发布功能允许设置文件在指定时间自动发布。

[配合-FILE08] 请提供预约发布管理页面的截图

#### 5.4.1 创建预约发布

**操作步骤**：
1. 点击"创建预约发布"按钮
2. 设置预约发布信息：
   - 选择文件版本
   - 设置发布时间
   - 选择发布目标
   - 输入备注信息
3. 点击"创建预约"按钮

#### 5.4.2 预约发布列表

显示所有的预约发布任务：
- **文件信息**：文件类型和版本
- **预约时间**：计划执行的时间
- **发布目标**：目标线路或终端
- **任务状态**：等待中、执行中、已完成、已取消
- **创建时间**：预约任务创建时间

**任务操作**：
- **编辑**：修改预约时间和目标（仅限等待中的任务）
- **取消**：取消预约任务
- **立即执行**：立即执行预约任务

---

## 6. 业务配置

业务配置模块包含系统运营所需的各种业务参数和配置。

### 6.1 线路参数管理

线路参数管理用于配置公交线路的票价信息。

[配合-BUS01] 请提供线路参数管理页面的截图

#### 6.1.1 线路票价列表

显示所有线路的票价配置：
- **线路编号**：公交线路的编号
- **线路名称**：公交线路的名称
- **票价信息**：各种票价类型和金额
- **有效期**：票价的有效时间范围
- **状态**：启用/禁用状态

#### 6.1.2 编辑线路票价

**操作步骤**：
1. 在线路列表中点击"编辑"按钮
2. 在编辑页面修改票价信息：
   - 基础票价设置
   - 分段票价配置
   - 优惠政策设置
3. 点击"保存"按钮

[配合-BUS02] 请提供线路票价编辑页面的截图

#### 6.1.3 票价版本管理

系统支持票价的版本管理：
- 查看历史版本
- 版本比较功能
- 版本回滚操作
- 版本发布记录

[配合-BUS03] 请提供票价版本管理页面的截图

### 6.2 银联密钥管理

银联密钥管理用于配置终端的银联支付密钥。

[配合-PAY01] 请提供银联密钥管理页面的截图

#### 6.2.1 密钥列表

显示所有终端的银联密钥信息：
- **终端号**：银联终端号
- **商户号**：银联商户号
- **密钥信息**：加密后的密钥数据
- **有效期**：密钥的有效时间
- **状态**：密钥状态

#### 6.2.2 密钥配置

**操作步骤**：
1. 点击"添加密钥"或"编辑"按钮
2. 填写密钥信息：
   - 终端号和商户号
   - 密钥数据
   - 有效期设置
3. 点击"保存"按钮

**安全注意事项**：
- 密钥数据会进行加密存储
- 只有授权用户才能查看和修改密钥
- 密钥变更会记录操作日志

### 6.3 车辆管理

车辆管理用于维护公交车辆的基本信息。

[配合-VEH01] 请提供车辆管理页面的截图

#### 6.3.1 车辆列表

显示所有车辆的基本信息：
- **车牌号**：车辆的车牌号码
- **车辆类型**：车辆的类型分类
- **所属线路**：车辆运营的线路
- **所属商户**：车辆所属的商户
- **状态**：车辆的运营状态

#### 6.3.2 添加车辆

**操作步骤**：
1. 点击"添加车辆"按钮
2. 填写车辆信息：
   - 车牌号（必填）
   - 车辆类型
   - 所属线路
   - 备注信息
3. 点击"保存"按钮

[配合-VEH02] 请提供添加车辆对话框的截图

### 6.4 字典管理

字典管理用于维护系统中的各种字典数据。

[配合-DICT01] 请提供字典管理页面的截图

#### 6.4.1 字典分类

系统包含以下字典分类：
- **终端类型**：定义各种终端设备类型
- **线路类型**：定义公交线路的分类
- **票价类型**：定义各种票价的类型
- **状态类型**：定义各种状态的分类

#### 6.4.2 字典项管理

每个字典分类包含多个字典项：
- **字典键**：字典项的唯一标识
- **字典值**：字典项的显示名称
- **排序**：字典项的显示顺序
- **状态**：启用/禁用状态

### 6.5 未注册线路管理

未注册线路管理用于处理系统中发现但未正式注册的线路。

[配合-LINE01] 请提供未注册线路管理页面的截图

**功能特点**：
- 自动发现未注册的线路
- 支持批量注册操作
- 线路信息补全功能
- 重复线路检测

---

## 7. 系统管理

系统管理模块提供系统级别的配置和管理功能，主要面向系统管理员。

### 7.1 用户管理

用户管理页面用于管理系统中的所有用户账户。

[配合-USER01] 请提供用户管理页面的截图，包含：
- 用户列表表格
- 搜索筛选功能
- 添加用户按钮
- 用户操作按钮

#### 7.1.1 用户列表

显示系统中的所有用户：
- **用户名**：用户的登录名
- **真实姓名**：用户的真实姓名
- **邮箱**：用户的邮箱地址
- **所属商户**：用户关联的商户
- **角色**：用户的系统角色
- **状态**：账户状态（正常/锁定）
- **创建时间**：账户创建时间
- **最后登录**：最后登录时间

#### 7.1.2 添加用户

**操作步骤**：
1. 点击"添加用户"按钮
2. 填写用户信息：
   - 用户名（必填，不可重复）
   - 真实姓名（必填）
   - 邮箱地址（必填）
   - 初始密码（必填）
   - 所属商户（可选）
   - 分配角色（必填）
3. 点击"创建用户"按钮

[配合-USER02] 请提供添加用户对话框的截图

**注意事项**：
- 用户名创建后不能修改
- 初始密码需符合密码策略要求
- 系统管理员可以为用户分配任意商户
- 商户管理员只能创建本商户的用户

#### 7.1.3 编辑用户

**操作步骤**：
1. 在用户列表中点击"编辑"按钮
2. 修改用户信息（用户名不可修改）
3. 点击"保存"按钮

[配合-USER03] 请提供编辑用户对话框的截图

#### 7.1.4 用户操作

每个用户记录提供以下操作：
- **编辑**：修改用户基本信息
- **重置密码**：为用户重置密码
- **锁定/解锁**：锁定或解锁用户账户
- **删除**：删除用户账户（谨慎操作）

**重置密码操作**：
1. 点击用户的"重置密码"按钮
2. 系统生成临时密码
3. 将临时密码告知用户
4. 用户首次登录时需要修改密码

[配合-USER04] 请提供重置密码对话框的截图

### 7.2 角色管理

角色管理页面用于管理系统中的用户角色。

[配合-ROLE01] 请提供角色管理页面的截图

#### 7.2.1 系统角色

系统预定义了以下角色：
- **SystemAdmin**：系统管理员，拥有所有权限
- **MerchantAdmin**：商户管理员，管理商户相关功能
- **Operator**：操作员，执行日常操作任务

#### 7.2.2 角色权限配置

**操作步骤**：
1. 选择要配置的角色
2. 在权限列表中勾选/取消权限
3. 点击"保存权限"按钮

[配合-ROLE02] 请提供角色权限配置页面的截图

**权限分类**：
- **用户管理权限**：用户的增删改查权限
- **终端管理权限**：终端的查看和操作权限
- **文件管理权限**：文件的上传、发布权限
- **系统设置权限**：系统配置的修改权限

### 7.3 商户管理

商户管理页面用于管理系统中的商户信息。

[配合-MERCH01] 请提供商户管理页面的截图

#### 7.3.1 商户列表

显示系统中的所有商户：
- **商户ID**：商户的唯一标识
- **商户名称**：商户的显示名称
- **公司名称**：商户的公司全称
- **联系人**：商户的联系人
- **联系信息**：联系电话或邮箱
- **运维人员**：负责的运维人员
- **状态**：商户的启用状态

#### 7.3.2 添加商户

**操作步骤**：
1. 点击"添加商户"按钮
2. 填写商户信息：
   - 商户ID（必填，不可重复）
   - 商户名称（必填）
   - 公司名称（可选）
   - 联系人信息（可选）
   - 运维人员（可选）
3. 点击"创建商户"按钮

[配合-MERCH02] 请提供添加商户对话框的截图

#### 7.3.3 商户操作

- **编辑**：修改商户基本信息
- **激活/停用**：控制商户的启用状态
- **查看详情**：查看商户的详细信息和统计数据

### 7.4 菜单管理

菜单管理页面用于配置系统的导航菜单。

[配合-MENU01] 请提供菜单管理页面的截图

#### 7.4.1 菜单结构

系统菜单采用分组结构：
- **菜单分组**：菜单的顶级分类
- **菜单项**：分组下的具体菜单项
- **外部系统**：集成的外部系统链接

#### 7.4.2 菜单配置

**菜单分组配置**：
- 分组标题和图标
- 显示顺序
- 角色可见性设置

**菜单项配置**：
- 菜单标题和图标
- 链接地址
- 角色权限设置
- 外部链接配置

[配合-MENU02] 请提供菜单配置对话框的截图

### 7.5 系统设置

系统设置页面用于配置系统的各种参数。

[配合-SET01] 请提供系统设置页面的截图

#### 7.5.1 基本设置

**系统信息设置**：
- 系统名称和版本
- 系统描述信息
- 联系方式设置

**安全设置**：
- 密码策略配置
- 会话超时设置
- 登录失败锁定策略

[配合-SET02] 请提供安全设置区域的截图

#### 7.5.2 功能开关

**功能模块开关**：
- 双因素认证开关
- 微信登录开关
- 文件上传功能开关
- 审计日志开关

**业务功能开关**：
- 自动注册开关
- 预约发布开关
- 批量操作开关

[配合-SET03] 请提供功能开关设置的截图

#### 7.5.3 邮件设置

**SMTP配置**：
- 邮件服务器地址
- 端口和加密设置
- 认证信息配置
- 发件人信息设置

**邮件模板**：
- 密码重置邮件模板
- 账户通知邮件模板
- 系统告警邮件模板

### 7.6 功能权限管理

功能权限管理页面用于配置系统的细粒度权限控制。

[配合-PERM01] 请提供功能权限管理页面的截图

#### 7.6.1 权限配置

**全局权限开关**：
- 控制功能的全局启用/禁用
- 影响所有用户的功能访问

**角色权限覆盖**：
- 为特定角色设置权限例外
- 支持允许/拒绝两种策略

[配合-PERM02] 请提供权限配置详细页面的截图

#### 7.6.2 权限测试

系统提供权限测试功能：
- 模拟用户权限检查
- 权限配置验证
- 权限冲突检测

---

## 8. 审计日志

审计日志模块记录系统中的各种操作和事件，用于安全审计和问题追踪。

### 8.1 登录日志

登录日志记录所有用户的登录活动。

[配合-LOG01] 请提供登录日志页面的截图

#### 8.1.1 日志信息

登录日志包含以下信息：
- **用户信息**：用户名、真实姓名
- **商户信息**：所属商户名称
- **登录时间**：登录的具体时间
- **IP地址**：登录的来源IP
- **用户代理**：浏览器和设备信息
- **登录结果**：成功/失败状态
- **失败原因**：登录失败的具体原因

#### 8.1.2 日志筛选

支持多种筛选条件：
- 按用户名筛选
- 按商户筛选
- 按时间范围筛选
- 按登录结果筛选
- 按IP地址筛选

### 8.2 操作日志

操作日志记录用户在系统中的各种操作。

[配合-LOG02] 请提供操作日志页面的截图

#### 8.2.1 日志信息

操作日志包含以下信息：
- **操作用户**：执行操作的用户
- **操作模块**：操作所属的功能模块
- **操作类型**：增加、修改、删除等
- **操作目标**：操作的具体对象
- **操作时间**：操作的执行时间
- **操作结果**：成功/失败状态
- **操作详情**：操作的详细描述

#### 8.2.2 操作分类

系统记录以下类型的操作：
- **用户管理操作**：用户的增删改操作
- **终端管理操作**：终端的配置和控制操作
- **文件管理操作**：文件的上传和发布操作
- **系统配置操作**：系统设置的修改操作

### 8.3 密码变更日志

密码变更日志记录所有的密码修改活动。

[配合-LOG03] 请提供密码变更日志页面的截图

#### 8.3.1 日志信息

密码变更日志包含：
- **用户信息**：密码被修改的用户
- **变更时间**：密码修改的时间
- **变更类型**：主动修改/管理员重置
- **操作员**：执行密码重置的管理员
- **IP地址**：操作的来源IP
- **变更原因**：密码修改的原因

#### 8.3.2 安全分析

密码变更日志用于：
- 检测异常的密码修改活动
- 追踪密码安全事件
- 验证密码策略的执行情况
- 分析用户的密码使用习惯

---

## 9. 个人设置

个人设置模块允许用户管理自己的账户信息和偏好设置。

### 9.1 账户信息

用户可以查看和修改自己的基本账户信息。

[配合-PROF01] 请提供个人账户信息页面的截图

#### 9.1.1 基本信息

显示和编辑以下信息：
- **用户名**：登录用户名（不可修改）
- **真实姓名**：用户的真实姓名
- **邮箱地址**：用户的邮箱地址
- **所属商户**：用户关联的商户（不可修改）
- **用户角色**：用户的系统角色（不可修改）

#### 9.1.2 修改个人信息

**操作步骤**：
1. 点击"编辑信息"按钮
2. 修改可编辑的字段
3. 点击"保存"按钮

### 9.2 密码管理

用户可以修改自己的登录密码。

[配合-PROF02] 请提供密码修改页面的截图

#### 9.2.1 修改密码

**操作步骤**：
1. 点击"修改密码"按钮
2. 输入当前密码
3. 输入新密码（需符合密码策略）
4. 确认新密码
5. 点击"修改密码"按钮

**密码策略要求**：
- 最少8位字符
- 包含大小写字母、数字和特殊字符
- 不能与最近3次使用的密码相同
- 不能包含用户名或邮箱

### 9.3 双因素认证设置

用户可以启用或禁用双因素认证。

[配合-PROF03] 请提供双因素认证设置页面的截图

#### 9.3.1 启用2FA

**操作步骤**：
1. 点击"启用双因素认证"按钮
2. 使用身份验证器应用扫描二维码
3. 输入验证码确认设置
4. 保存备用恢复代码

#### 9.3.2 禁用2FA

**操作步骤**：
1. 点击"禁用双因素认证"按钮
2. 输入当前密码确认
3. 输入2FA验证码
4. 确认禁用操作

### 9.4 微信绑定

用户可以绑定微信账号用于快速登录。

[配合-PROF04] 请提供微信绑定页面的截图

#### 9.4.1 绑定微信

**操作步骤**：
1. 点击"绑定微信"按钮
2. 使用微信扫描二维码
3. 在微信中确认绑定
4. 系统显示绑定成功

#### 9.4.2 解绑微信

**操作步骤**：
1. 点击"解绑微信"按钮
2. 输入当前密码确认
3. 确认解绑操作

### 9.5 偏好设置

用户可以设置个人的使用偏好。

[配合-PROF05] 请提供偏好设置页面的截图

#### 9.5.1 界面设置

- **主题模式**：明亮/暗黑主题切换
- **语言设置**：界面语言选择
- **时区设置**：时间显示时区
- **分页大小**：列表页面的默认分页大小

#### 9.5.2 通知设置

- **邮件通知**：启用/禁用邮件通知
- **系统消息**：启用/禁用系统内消息
- **通知类型**：选择接收的通知类型

---

## 10. 常见问题

### 10.1 登录问题

#### Q1: 忘记密码怎么办？
**解决方案**：
1. 在登录页面点击"忘记密码"链接
2. 输入用户名或邮箱地址
3. 查收邮件中的重置链接
4. 按照邮件指引重置密码

#### Q2: 双因素认证验证码错误？
**解决方案**：
1. 检查手机时间是否准确
2. 确认使用的是正确的身份验证器应用
3. 重新同步身份验证器的时间
4. 如仍无法解决，联系管理员重置2FA

#### Q3: 账户被锁定怎么办？
**解决方案**：
1. 等待锁定时间自动解除（通常15-30分钟）
2. 联系系统管理员手动解锁
3. 检查是否有密码输入错误的情况

### 10.2 操作问题

#### Q4: 无法看到某些菜单项？
**解决方案**：
1. 检查用户角色是否有相应权限
2. 确认功能是否已全局启用
3. 联系管理员检查权限配置

#### Q5: 文件上传失败？
**解决方案**：
1. 检查文件大小是否超过限制（100MB）
2. 确认文件格式是否支持
3. 检查网络连接是否稳定
4. 尝试重新上传文件

#### Q6: 终端状态显示异常？
**解决方案**：
1. 刷新页面获取最新状态
2. 检查终端设备的网络连接
3. 查看终端事件日志了解详情
4. 联系技术支持进行故障排查

### 10.3 性能问题

#### Q7: 页面加载缓慢？
**解决方案**：
1. 检查网络连接速度
2. 清除浏览器缓存
3. 减少筛选条件范围
4. 联系管理员检查服务器状态

#### Q8: 数据导出时间过长？
**解决方案**：
1. 缩小导出的时间范围
2. 减少导出的字段数量
3. 分批次导出大量数据
4. 在系统负载较低时进行导出

### 10.4 浏览器兼容性

#### 支持的浏览器版本：
- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

#### 推荐设置：
- 启用JavaScript
- 允许Cookie
- 启用本地存储
- 屏幕分辨率：1366x768或更高

### 10.5 移动端使用

#### 移动端功能限制：
- 部分复杂表格在小屏幕上会隐藏某些列
- 文件上传功能在移动端可能受限
- 建议使用横屏模式获得更好体验

#### 移动端操作提示：
- 使用双指缩放查看详细内容
- 左右滑动查看表格的隐藏列
- 长按表格行可显示操作菜单

---

## 附录

### A. 快捷键列表

| 功能 | 快捷键 | 说明 |
|------|--------|------|
| 搜索 | Ctrl + F | 在当前页面搜索 |
| 刷新 | F5 | 刷新当前页面 |
| 新建 | Ctrl + N | 在支持的页面创建新记录 |
| 保存 | Ctrl + S | 保存当前编辑的内容 |
| 取消 | Esc | 取消当前操作或关闭对话框 |

### B. 系统限制

| 项目 | 限制值 | 说明 |
|------|--------|------|
| 文件上传大小 | 100MB | 单个文件的最大大小 |
| 用户名长度 | 3-50字符 | 用户名的长度限制 |
| 密码长度 | 8-128字符 | 密码的长度限制 |
| 会话超时 | 120分钟 | 用户无操作的超时时间 |
| 分页大小 | 10-100条 | 列表页面的分页限制 |

### C. 联系方式

如遇到技术问题或需要帮助，请联系：

- **技术支持邮箱**: <EMAIL>
- **系统管理员**: <EMAIL>
- **紧急联系电话**: 400-xxx-xxxx

---

**文档版本**: v1.0
**最后更新**: 2025-08-27
**维护人员**: 开发团队

*本操作说明书基于WebAdmin系统的实际功能编写，如有功能更新，请及时更新本文档。*
